#Requires -Version 5.1

<#
.SYNOPSIS
    修復 pm2-installer 命令不可用的問題
.DESCRIPTION
    解決 pm2-installer 安裝後命令無法識別的問題，並配置 Windows 服務
.AUTHOR
    Development Team
.VERSION
    1.0.0
#>

# 設定錯誤處理
$ErrorActionPreference = "Stop"

# 檢查管理員權限
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

# 刷新環境變數
function Update-EnvironmentVariables {
    Write-Info "刷新環境變數..."
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
}

function Fix-PM2InstallerPath {
    Write-Info "修復 pm2-installer 路徑問題..."
    
    try {
        # 獲取 npm 全域安裝路徑
        $npmGlobalPath = npm config get prefix 2>$null
        if ($npmGlobalPath) {
            Write-Info "npm 全域路徑: $npmGlobalPath"
            
            # 檢查 pm2-installer 是否存在於 npm 全域目錄
            $pm2InstallerPath = Join-Path $npmGlobalPath "pm2-installer.cmd"
            $pm2InstallerBat = Join-Path $npmGlobalPath "pm2-installer.bat"
            
            if (Test-Path $pm2InstallerPath) {
                Write-Success "找到 pm2-installer.cmd: $pm2InstallerPath"
                return $pm2InstallerPath
            } elseif (Test-Path $pm2InstallerBat) {
                Write-Success "找到 pm2-installer.bat: $pm2InstallerBat"
                return $pm2InstallerBat
            } else {
                Write-Warning "在 npm 全域目錄中未找到 pm2-installer"
                return $null
            }
        } else {
            Write-Error "無法獲取 npm 全域路徑"
            return $null
        }
    }
    catch {
        Write-Error "修復 pm2-installer 路徑失敗: $($_.Exception.Message)"
        return $null
    }
}

function Download-PM2Installer {
    Write-Info "下載 PM2 Windows 服務安裝器..."

    try {
        $installerPath = "C:\pm2-installer"

        # 檢查是否已下載
        if (Test-Path $installerPath) {
            Write-Success "PM2 Windows 服務安裝器已存在"
            return $installerPath
        }

        # 創建目錄
        New-Item -ItemType Directory -Path $installerPath -Force | Out-Null
        Set-Location $installerPath

        # 下載 pm2-installer 項目
        Write-Info "正在從 GitHub 下載 pm2-installer..."
        git clone https://github.com/jessety/pm2-installer.git . *>$null

        if (Test-Path "package.json") {
            Write-Success "PM2 Windows 服務安裝器下載完成"
            return $installerPath
        } else {
            Write-Error "下載失敗，未找到 package.json"
            return $null
        }
    }
    catch {
        Write-Error "下載 PM2 Windows 服務安裝器失敗: $($_.Exception.Message)"
        return $null
    }
}

function Install-PM2Service {
    Write-Info "嘗試安裝 PM2 Windows 服務..."

    try {
        # 檢查服務是否已存在
        $existingService = Get-Service -Name "PM2" -ErrorAction SilentlyContinue
        if ($existingService) {
            Write-Warning "PM2 服務已存在"
            return $true
        }

        # 下載 pm2-installer
        $installerPath = Download-PM2Installer
        if (-not $installerPath) {
            Write-Error "無法下載 PM2 Installer"
            return $false
        }

        # 保存 PM2 配置
        Write-Info "保存 PM2 進程配置..."
        pm2 save 2>$null | Out-Null

        # 切換到 pm2-installer 目錄
        $originalLocation = Get-Location
        Set-Location $installerPath

        try {
            # 根據官方文檔執行安裝步驟
            Write-Info "配置 npm 設定..."
            npm run configure *>$null

            Write-Info "配置 PowerShell 執行策略..."
            npm run configure-policy *>$null

            Write-Info "安裝 PM2 服務..."
            npm run setup *>$null

            # 等待服務安裝
            Start-Sleep -Seconds 10

            # 檢查服務是否安裝成功
            $newService = Get-Service -Name "PM2" -ErrorAction SilentlyContinue
            if ($newService) {
                Write-Success "PM2 服務安裝成功"

                # 設定為自動啟動
                Set-Service -Name "PM2" -StartupType Automatic
                Write-Success "PM2 服務已設定為自動啟動"

                # 啟動服務
                if ($newService.Status -ne "Running") {
                    Start-Service -Name "PM2"
                    Write-Success "PM2 服務已啟動"
                }

                return $true
            } else {
                Write-Error "PM2 服務安裝失敗"
                return $false
            }
        }
        finally {
            # 恢復原始位置
            Set-Location $originalLocation
        }
    }
    catch {
        Write-Error "安裝 PM2 服務時發生錯誤: $($_.Exception.Message)"
        return $false
    }
}

function Test-PM2Service {
    Write-Info "測試 PM2 服務狀態..."
    
    try {
        $service = Get-Service -Name "PM2" -ErrorAction SilentlyContinue
        if ($service) {
            Write-Success "PM2 服務已安裝"
            Write-ColorOutput "  服務狀態: $($service.Status)" "White"
            Write-ColorOutput "  啟動類型: $($service.StartType)" "White"
            
            if ($service.Status -eq "Running") {
                Write-Success "PM2 服務正在運行"
                
                # 檢查 PM2 進程
                Write-Info "檢查 PM2 進程..."
                pm2 list
                
                return $true
            } else {
                Write-Warning "PM2 服務未運行"
                return $false
            }
        } else {
            Write-Error "PM2 服務未安裝"
            return $false
        }
    }
    catch {
        Write-Error "測試 PM2 服務失敗: $($_.Exception.Message)"
        return $false
    }
}

function Show-TroubleshootingInfo {
    Write-ColorOutput "`n=== 故障排除信息 ===" "Magenta"
    
    try {
        # 檢查 npm 全域安裝
        Write-Info "檢查 npm 全域安裝..."
        $npmList = npm list -g --depth=0 2>$null
        if ($npmList -match "pm2-installer") {
            Write-Success "pm2-installer 已全域安裝"
        } else {
            Write-Error "pm2-installer 未全域安裝"
        }
        
        # 檢查 PATH 環境變數
        Write-Info "檢查 PATH 環境變數..."
        $npmPrefix = npm config get prefix 2>$null
        if ($npmPrefix -and $env:PATH -match [regex]::Escape($npmPrefix)) {
            Write-Success "npm 全域路徑已在 PATH 中"
        } else {
            Write-Warning "npm 全域路徑可能不在 PATH 中"
            Write-Info "npm 全域路徑: $npmPrefix"
        }
        
        # 檢查命令可用性
        Write-Info "檢查命令可用性..."
        $commands = @("npm", "node", "pm2", "pm2-installer", "npx")
        foreach ($cmd in $commands) {
            $cmdCheck = Get-Command $cmd -ErrorAction SilentlyContinue
            if ($cmdCheck) {
                Write-Success "$cmd 命令可用"
            } else {
                Write-Warning "$cmd 命令不可用"
            }
        }
    }
    catch {
        Write-Error "獲取故障排除信息失敗: $($_.Exception.Message)"
    }
}

# 主程序
function Main {
    try {
        # 檢查管理員權限
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }
        
        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green
        
        Write-ColorOutput "=== PM2 Installer 修復工具 ===" "Magenta"
        Write-ColorOutput "此工具將修復 pm2-installer 命令不可用的問題並配置 Windows 服務" "Gray"
        
        # 刷新環境變數
        Update-EnvironmentVariables
        
        # 顯示故障排除信息
        Show-TroubleshootingInfo
        
        # 嘗試安裝 PM2 服務
        Write-ColorOutput "`n=== 開始修復 ===" "Magenta"
        $installSuccess = Install-PM2Service
        
        # 測試服務狀態
        Write-ColorOutput "`n=== 測試結果 ===" "Magenta"
        $testSuccess = Test-PM2Service
        
        if ($installSuccess -and $testSuccess) {
            Write-Success "`n🎉 修復成功！PM2 Windows 服務已正確配置。"
            Write-Info "Uptime Kuma 現在會在系統重新啟動後自動運行。"
        } elseif ($installSuccess) {
            Write-Warning "`n⚠️ 服務已安裝但可能需要手動啟動。"
        } else {
            Write-Error "`n❌ 修復失敗，請檢查錯誤信息並重試。"
            Write-Info "您可以嘗試手動執行以下命令："
            Write-ColorOutput "  npx pm2-installer install" "Gray"
        }
        
        Write-Host "`n按任意鍵退出..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
    }
    catch {
        Write-Error "修復過程中發生錯誤: $($_.Exception.Message)"
        Write-ColorOutput "`n按任意鍵退出..." "Red"
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        exit 1
    }
}

# 執行主程序
Main
