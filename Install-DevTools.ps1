#Requires -Version 5.1

<#
.SYNOPSIS
    自動化安裝開發工具套件
.DESCRIPTION
    自動安裝 Chocolatey, Git, Node.js, PM2 和 Uptime Kuma 監控工具
.AUTHOR
    Development Team
.VERSION
    1.0.0
#>

# 設定錯誤處理
$ErrorActionPreference = "Stop"

# 彩色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

# 檢查管理員權限
function Test-Admin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 刷新環境變數
function Update-EnvironmentVariables {
    Write-Info "刷新環境變數..."
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
}

# 檢查命令是否存在
function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# 主要安裝函數
function Install-Chocolatey {
    Write-Info "正在安裝 Chocolatey..."
    if (Test-Command "choco") {
        Write-Success "Chocolatey 已安裝"
        return
    }
    
    try {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        Update-EnvironmentVariables
        Write-Success "Chocolatey 安裝完成"
    }
    catch {
        Write-Error "Chocolatey 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-Git {
    Write-Info "正在安裝 Git..."
    if (Test-Command "git") {
        Write-Success "Git 已安裝"
        return
    }
    
    try {
        Write-Info "正在通過 Chocolatey 安裝 Git..."
        choco install git -y *>$null
        Update-EnvironmentVariables
        Write-Success "Git 安裝完成"
    }
    catch {
        Write-Error "Git 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-NodeJS {
    Write-Info "正在安裝 Node.js..."
    if (Test-Command "node") {
        Write-Success "Node.js 已安裝"
        return
    }
    
    try {
        Write-Info "正在通過 Chocolatey 安裝 Node.js..."
        choco install nodejs -y *>$null
        Update-EnvironmentVariables
        Write-Success "Node.js 安裝完成"
    }
    catch {
        Write-Error "Node.js 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-PM2 {
    Write-Info "正在安裝 PM2..."
    if (Test-Command "pm2") {
        Write-Success "PM2 已安裝"
        return
    }
    
    try {
        Write-Info "正在全域安裝 PM2..."
        npm install -g pm2 --silent *>$null
        Update-EnvironmentVariables
        Write-Success "PM2 安裝完成"
    }
    catch {
        Write-Error "PM2 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-UptimeKuma {
    Write-Info "正在安裝 Uptime Kuma..."
    $installPath = "C:\uptime-kuma"
    
    if (Test-Path $installPath) {
        Write-Warning "Uptime Kuma 目錄已存在，跳過安裝"
        return $installPath
    }
    
    try {
        # 創建安裝目錄
        New-Item -ItemType Directory -Path $installPath -Force | Out-Null
        Set-Location $installPath
        
        # 下載並安裝特定版本
        Write-Info "正在下載 Uptime Kuma 源碼..."
        git clone https://github.com/louislam/uptime-kuma.git . *>$null
        git checkout 2.0.0-beta.2 *>$null

        # 安裝依賴
        Write-Info "正在安裝 Node.js 依賴（這可能需要幾分鐘）..."
        npm ci --production --silent *>$null
        Write-Info "正在下載預編譯資源..."
        npm run download-dist --silent *>$null
        
        Write-Success "Uptime Kuma 安裝完成"
        return $installPath
    }
    catch {
        Write-Error "Uptime Kuma 安裝失敗: $($_.Exception.Message)"
        throw
    }
}

function Install-PM2LogRotate {
    Write-Info "安裝 PM2 日誌輪轉插件..."

    try {
        # 設定環境變數以忽略 Node.js 棄用警告
        $env:NODE_NO_WARNINGS = "1"

        # 安裝 PM2 日誌輪轉插件（官方推薦）
        pm2 install pm2-logrotate 2>$null | Out-Null

        # 恢復環境變數
        $env:NODE_NO_WARNINGS = $null

        # 檢查插件是否安裝成功
        Start-Sleep -Seconds 2
        $pluginStatus = pm2 list | Select-String "pm2-logrotate.*online"

        if ($pluginStatus) {
            Write-Success "PM2 日誌輪轉插件安裝完成"
        } else {
            Write-Warning "PM2 日誌輪轉插件可能未正確安裝，但不影響主要功能"
        }
    }
    catch {
        Write-Warning "PM2 日誌輪轉插件安裝失敗，但不影響主要功能"
    }
}

function Start-UptimeKumaService {
    param([string]$UptimeKumaPath)

    Write-Info "啟動 Uptime Kuma 服務..."

    try {
        # 切換到 Uptime Kuma 目錄
        Set-Location $UptimeKumaPath

        # 停止現有的 uptime-kuma 進程（如果存在）
        try {
            pm2 delete uptime-kuma *>$null
        }
        catch {
            # 忽略刪除不存在進程的錯誤
        }

        # 使用官方推薦的方式啟動服務
        Write-Info "正在啟動 PM2 服務..."

        # 啟動服務並忽略 Node.js 棄用警告
        $env:NODE_NO_WARNINGS = "1"
        pm2 start server/server.js --name uptime-kuma 2>$null | Out-Null
        pm2 save 2>$null | Out-Null
        $env:NODE_NO_WARNINGS = $null

        # 驗證服務是否正常啟動
        Start-Sleep -Seconds 3
        $pm2Status = pm2 list | Select-String "uptime-kuma.*online"

        if ($pm2Status) {
            Write-Success "Uptime Kuma 服務已啟動"
            Write-Success "服務狀態驗證成功"
        } else {
            # 檢查服務是否以其他狀態存在
            $anyStatus = pm2 list | Select-String "uptime-kuma"
            if ($anyStatus) {
                Write-Warning "Uptime Kuma 服務已啟動，但狀態可能不是 online"
                Write-Info "請檢查服務狀態："
                pm2 list
            } else {
                throw "服務啟動失敗，PM2 列表中找不到 uptime-kuma 進程"
            }
        }
    }
    catch {
        Write-Error "啟動 Uptime Kuma 服務失敗: $($_.Exception.Message)"
        Write-Info "嘗試查看 PM2 狀態..."
        pm2 list

        # 檢查服務是否實際上已經在運行
        $runningStatus = pm2 list | Select-String "uptime-kuma.*online"
        if ($runningStatus) {
            Write-Warning "雖然出現錯誤，但服務似乎已經在運行中"
            Write-Success "可以嘗試訪問 http://localhost:3001"
            return  # 不拋出異常，繼續執行
        } else {
            throw
        }
    }
}

function Show-InstallationSummary {
    Write-ColorOutput ("`n" + "="*60) "Magenta"
    Write-ColorOutput "           安裝完成摘要" "Magenta"
    Write-ColorOutput ("="*60) "Magenta"
    
    Write-Success "所有工具已成功安裝："
    Write-ColorOutput "  • Chocolatey - Windows 套件管理器" "White"
    Write-ColorOutput "  • Git - 版本控制系統" "White"
    Write-ColorOutput "  • Node.js - JavaScript 運行環境" "White"
    Write-ColorOutput "  • PM2 - 進程管理器" "White"
    Write-ColorOutput "  • Uptime Kuma 2.0.0-beta.2 - 監控工具" "White"
    
    Write-ColorOutput "`n常用 PM2 命令：" "Yellow"
    Write-ColorOutput "  pm2 list                    # 查看所有進程" "Gray"
    Write-ColorOutput "  pm2 logs uptime-kuma        # 查看日誌" "Gray"
    Write-ColorOutput "  pm2 restart uptime-kuma     # 重啟服務" "Gray"
    Write-ColorOutput "  pm2 stop uptime-kuma        # 停止服務" "Gray"
    Write-ColorOutput "  pm2 start uptime-kuma       # 啟動服務" "Gray"
    Write-ColorOutput "  pm2 monit                   # 監控面板" "Gray"
    
    Write-ColorOutput "`nUptime Kuma 訪問信息：" "Yellow"
    Write-ColorOutput "  URL: http://localhost:3001" "Green"
    Write-ColorOutput "  安裝路徑: C:\uptime-kuma" "Gray"
    Write-ColorOutput "  PM2 日誌: pm2 logs uptime-kuma" "Gray"
    
    Write-ColorOutput "`n正在開啟瀏覽器..." "Cyan"
}

# 主程序
function Main {
    try {
        # 檢查管理員權限
        if (-not (Test-Admin)) {
            Write-Host "Not running as Administrator. Restarting with elevated privileges..." -ForegroundColor Yellow
            $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" " + $args
            Start-Process powershell -Verb runAs -ArgumentList $arguments
            Exit
        }

        Write-Host "Running with Administrator privileges ✓" -ForegroundColor Green

        # 設定執行策略
        Set-ExecutionPolicy Bypass -Scope Process -Force
        Write-Success "執行策略已設定"
        
        Write-ColorOutput "`n開始自動化安裝程序..." "Magenta"
        Write-ColorOutput ("="*50) "Magenta"
        
        # 按順序安裝
        Install-Chocolatey
        Install-Git
        Install-NodeJS
        Install-PM2
        $uptimeKumaPath = Install-UptimeKuma

        # 安裝 PM2 插件和啟動服務
        Install-PM2LogRotate
        Start-UptimeKumaService -UptimeKumaPath $uptimeKumaPath
        
        # 等待服務啟動
        Write-Info "等待服務啟動..."
        Start-Sleep -Seconds 5
        
        # 顯示摘要
        Show-InstallationSummary
        
        # 開啟瀏覽器
        Start-Process "http://localhost:3001"
        
        Write-ColorOutput "`n按任意鍵退出..." "Yellow"
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
    }
    catch {
        Write-Error "安裝過程中發生錯誤: $($_.Exception.Message)"
        Write-ColorOutput "`n按任意鍵退出..." "Red"
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        exit 1
    }
}

# 執行主程序
Main